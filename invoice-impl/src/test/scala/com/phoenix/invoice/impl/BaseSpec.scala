package com.phoenix.invoice.impl

import akka.stream.Materializer
import akka.stream.scaladsl.Source
import akka.{Done, NotUsed}
import com.lightbend.lagom.scaladsl.api.ServiceCall
import com.lightbend.lagom.scaladsl.api.broker.Topic
import com.lightbend.lagom.scaladsl.server.LocalServiceLocator
import com.lightbend.lagom.scaladsl.testkit.ServiceTest.Setup
import com.lightbend.lagom.scaladsl.testkit.{ProducerStub, ProducerStubFactory, ServiceTest, TestTopicComponents}
import com.phoenix.agency.api
import com.phoenix.agency.api.request.{ActivationRequest, DeactivationRequest, IntegrationRequest, Logo, TestAgenciesRequest, Theme, ThemeRef, ThemeRgba}
import com.phoenix.agency.api.response.{Agency, AgencyGroup, AgencyGroupResponse, AgencyListingResponse, IntegrationResponse, IntegrationTypeResponse, InvitableParty, PropertyInspectOAuthResponse}
import com.phoenix.agency.api.{AgencyEvent, AgencyGroupEvent, AgencyService, Agency_v1, CreateSegment, GroupMember, Invitation, InvitationOTPRequest, InvitationReply, InvitationRequest, Member}
import com.phoenix.authentication.Encryption
import com.phoenix.authentication.jwt._
import com.phoenix.invoice.api.InvoiceService
import com.phoenix.invoice.impl.BaseSpec.WalletServiceStub
import com.phoenix.invoice.impl.events.InvoiceEvent
import com.phoenix.lightstone.api.Request
import com.phoenix.party.api.Company_v1
import com.phoenix.party.api.request.Base64Request
import com.phoenix.portfolio.api._
import com.phoenix.portfolio.api.property.request.{PropertyList, PropertyUpdate}
import com.phoenix.portfolio.api.property.response.{PropertyResultRow, PropertySearchResult}
import com.phoenix.portfolio.api.property.{PropertyEvent, Property_v1}
import com.phoenix.projections.api.State
import com.phoenix.propdata.api.{Request, Response}
import com.phoenix.propertyinspect.api.Response
import com.phoenix.util.UUID
import com.phoenix.wallet.api._
import com.typesafe.config.ConfigFactory
import org.scalatest.BeforeAndAfterAll
import org.scalatest.concurrent.{Eventually, ScalaFutures}
import org.scalatest.matchers.should.Matchers
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.wordspec.AsyncWordSpec

import java.util
import scala.concurrent.{ExecutionContext, Future}
import scala.util.Random

trait BaseSpec extends AsyncWordSpec with Matchers with BeforeAndAfterAll with Eventually with ScalaFutures {
  protected lazy val portfolioStub: ProducerStub[PortfolioEvent] = server.application.portfolioEventStub

  protected lazy val applicationFeeStub: ProducerStub[ApplicationFeeEvent] = server.application.applicationFeeEventStub
  protected lazy val invoiceStub: ProducerStub[InvoiceEvent] = server.application.invoiceEventStub
  protected lazy val blockchainStub: ProducerStub[BlockchainEvent] = server.application.blockchainEventStub
  protected lazy val propertyStub: ProducerStub[PropertyEvent] = server.application.propertyEventStub
  protected lazy val walletEventStub: ProducerStub[WalletEvent] = server.application.walletEventStub

  override protected def afterAll(): Unit = server.stop()

  override protected def beforeAll(): Unit = server

  // Listeners to listen to when starting server
  protected val listeners: Seq[String]

  protected val serviceSetup: Setup = ServiceTest.defaultSetup.withCassandra(true)

  private def randomString(length: Int = 64) = Random.alphanumeric.take(length).mkString("")
  protected val secretKey = ConfigFactory.load().getString("play.http.secret.key")
  protected val totpSecret = Encryption.encrypt(secretKey, randomString(64))

  protected val tokenContent: UserSession =
    UserSession(UUID.createV4, Some(AgencyMembership.Owner(UUID.createV4)), "Test", "<EMAIL>", Some("user"), totpSecret, None, None)
  protected val agencyId: UUID = tokenContent.agencyMembership.map(_.agencyId).get
  protected val userId: UUID = tokenContent.userId
  protected val sendingProofToEmail = "<EMAIL>"
  protected val sendingProofSubject = "Test subject"

  protected lazy val server = {
    ServiceTest.startServer(serviceSetup) { ctx =>
      new InvoiceApplication(ctx) with LocalServiceLocator with TestTopicComponents {
        val stubFactory = new ProducerStubFactory(actorSystem, materializer)
        val portfolioEventStub = stubFactory.producer[PortfolioEvent](PortfolioService.PORTFOLIO_TOPIC)
        val propertyEventStub = stubFactory.producer[PropertyEvent](PortfolioService.PROPERTY_TOPIC)
        val applicationFeeEventStub = stubFactory.producer[ApplicationFeeEvent](PortfolioService.APPLICATION_FEE_TOPIC)
        val blockchainEventStub = stubFactory.producer[BlockchainEvent](WalletService.BLOCKCHAIN_TOPIC_NAME)
        val invoiceEventStub = stubFactory.producer[InvoiceEvent](InvoiceService.TOPIC_NAME)
        val walletEventStub = stubFactory.producer[WalletEvent](WalletService.TOPIC_NAME)
        val agencyEventStub = stubFactory.producer[AgencyEvent](AgencyService.TOPIC_NAME)
        val agencyGroupEventStub = stubFactory.producer[AgencyGroupEvent](AgencyService.AGENCY_GROUP_TOPIC_NAME)

        override lazy val walletService = new WalletServiceStub(walletEventStub, blockchainEventStub)
        override lazy val portfolioService = new BaseSpec.PortfolioServiceStub(portfolioEventStub, propertyEventStub, applicationFeeEventStub)
        override lazy val agencyService: AgencyService = new AgencyServiceStub(agencyEventStub, agencyGroupEventStub)

        subscribeSome(listeners: _*)
      }
    }
  }

  lazy val client = server.serviceClient.implement[InvoiceService]
  lazy val auth: AuthenticationService = server.application.authenticationService

  implicit protected lazy val mat: Materializer = server.materializer
  override implicit val patienceConfig: PatienceConfig = PatienceConfig(scaled(Span(120, Seconds)), scaled(Span(15, Millis)))


  protected lazy val persistentEntityRegistry = server.application.persistentEntityRegistry

  // Connectors
  protected lazy val portfolioConnector = server.application.portfolioConnector
  protected lazy val propertyConnector = server.application.propertyConnector
  protected lazy val invoiceRepository = server.application.invoiceRepository
  protected lazy val partyRepository = server.application.partyRepository
  protected lazy val depositFeeConnector = server.application.depositFeeConnector
}

object BaseSpec {
  import com.phoenix.portfolio.{api => externalEvents}

  class PortfolioServiceStub(stub: ProducerStub[externalEvents.PortfolioEvent],
                             propertyEventProducerStub: ProducerStub[PropertyEvent],
                             applicationFeeProducerStub: ProducerStub[ApplicationFeeEvent])
    extends PortfolioService {
    import akka.{Done, NotUsed}
    import com.lightbend.lagom.scaladsl.api.ServiceCall
    import com.lightbend.lagom.scaladsl.api.broker.Topic
    import com.phoenix.portfolio.api.request._
    import com.phoenix.portfolio.api.response._

    override def updateProperty(id: String): ServiceCall[PropertyUpdate, Property_v1] = ???

    override def portfolioSummaries: ServiceCall[NotUsed, Seq[PortfolioSummaryRow]] = ???

    override def bulkInvoicesCsvTemplate: ServiceCall[NotUsed, Base64CsvResponse] = ???

    override def getStats(): ServiceCall[NotUsed, PortfoliosStats] = ???

    override def getProperties = ???

    override def invoiceSchedules(id: String): ServiceCall[NotUsed, InvoiceSchedules] = ???

    override def getPropertyPortfolios(id: String): ServiceCall[NotUsed, Seq[Portfolio_v1]] = ???

    override def addLeaseTerms(id: String) = ???

    override def amendCommission(id: String) = ???

    override def amendContract(id: String) = ???

    override def amendContractV2(id: String) = ???

    override def amendProperty(id: String) = ???

    override def amendSettings(id: String) = ???

    override def createInvoiceTemplate(id: String) = ???

    override def getPortfolio(id: String) = ???

    override def openPortfolio = ???

    override def removeInvoiceTemplate(id: String, invoice_id: String) = ???

    override def updateInvoiceTemplate(id: String, invoice_id: String) = ???

    override def updateInvoiceTemplateV2(id: String, invoice_id: String) = ???

    override def addAgent(id: String): ServiceCall[AgentFields, Done] = ???

    override def removeAgent(id: String, person_id: String) = ???

    override def requestApproval(id: String): ServiceCall[ApprovalFields, Done] = ???

    override def approve(id: String): ServiceCall[ApprovalFields, Done] = ???

    override def decline(id: String): ServiceCall[DeclineFields, Done] = ???

    override def portfolioEvents(): Topic[externalEvents.PortfolioEvent] = stub.topic

    override def createProperty = ???

    override def deleteProperty(id: String) = ???

    override def getProperty(id: String) = ???

    override def getGooglePlace(placeId: String) = ???

    override def addPropertyLandlord(id: String) = ???

    override def combinedSearchProperties(query: String) = ???

    override def advancedSearchProperties() = ???

    override def searchProperties(query: String): ServiceCall[NotUsed, PropertySearchResult] = ???

    override def propertyEvents(): Topic[PropertyEvent] = propertyEventProducerStub.topic

    override def getPortfolios = ???

    override def renew(id: String) = ???

    override def deleteRenew(id: String) = ???

    override def terminationReasons() = ???

    override def terminateLease(id: String) = ???

    override def deleteLease(id: String) = ???

    override def amendSegments(id: String) = ???

    override def updateOwnerParties(id: String) = ???

    override def updateTenantParties(id: String) = ???

    def portfolioReportEvents() = ???

    def propertyReportEvents() = ???

    override def addLeaseTermsV2(id: String): ServiceCall[LeaseTermsFields_v1, Portfolio_v1] = ???

    override def amendCommissionV2(id: String): ServiceCall[CommissionSplit_v1, Portfolio_v1] = ???

    override def amendPropertyV2(id: String): ServiceCall[PortfolioPropertyFields, Portfolio_v1] = ???

    override def amendSettingsV2(id: String): ServiceCall[SettingsFields, Portfolio_v1] = ???

    override def amendSegmentsV2(id: String): ServiceCall[SegmentFields, Portfolio_v1] = ???

    override def createInvoiceTemplateV2(id: String): ServiceCall[InvoiceTemplateFields, Portfolio_v1] = ???

    override def openPortfolioV2: ServiceCall[OpenPortfolioFields, Portfolio_v1] = ???

    override def removeInvoiceTemplateV2(id: String, invoice_id: String): ServiceCall[NotUsed, Portfolio_v1] = ???

    override def approveV2(id: String): ServiceCall[ApprovalFields, Portfolio_v1] = ???

    override def renewV2(id: String): ServiceCall[Renewal, Portfolio_v1] = ???

    override def deleteRenewV2(id: String): ServiceCall[NotUsed, Portfolio_v1] = ???

    override def terminateLeaseV2(id: String): ServiceCall[TerminationFields, Portfolio_v1] = ???

    override def deleteLeaseV2(id: String): ServiceCall[NotUsed, Portfolio_v1] = ???

    override def leaseStatus: ServiceCall[Seq[UUID], Seq[PortfoliosStatusRow]] = ???

    def getProjections = ???

    def startProjection(name: String) = ???

    def stopProjection(name: String) = ???

    def startProjectionWorker(name: String, worker: String) = ???

    def stopProjectionWorker(name: String, worker: String) = ???

    override def acceptApplication(portfolioId: String, applicationId: String): ServiceCall[NotUsed, Portfolio_v1] = ???

    override def applicationFeeEvents(): Topic[ApplicationFeeEvent] = applicationFeeProducerStub.topic

    override def addApplication(portfolioId: String): ServiceCall[AddApplicationFields, ApplicationFeeResponse] = ???

    override def updateApplication(id: String, applicationId: String): ServiceCall[AddApplicationFields, ApplicationFeeResponse] = ???

    override def getApplications(portfolioId: String): ServiceCall[NotUsed, ApplicationFeeResponse] = ???

    override def removeApplication(portfolioId: String, applicationId: String): ServiceCall[NotUsed, ApplicationFeeResponse] = ???

    override def getPropertiesFromListInternal(): ServiceCall[PropertyList, Seq[Property_v1]] = ???

    override def portfolioSummariesV2: ServiceCall[NotUsed, Seq[PortfolioWithApplications]] = ???

    override def amendMetaData(id: String) = ???

    override def getLightstoneProperty(lightstoneId: String): ServiceCall[NotUsed, LightstoneResult] = ???

    override def getPortfoliosByAgency(agencyId: String): ServiceCall[NotUsed, Seq[Portfolio_v1]] = ???

    override def getPortfolioInternal(id: String): ServiceCall[NotUsed, OptionalPortfolioResponse] = ???

    override def getPortfoliosByAgencyInternal(agencyId: String): ServiceCall[NotUsed, Seq[Portfolio_v1]] = ???

    override def getPropertiesV2(agencyId: String) = ???

    override def getApplicationsByAgency(agencyId: String): ServiceCall[NotUsed, ApplicationResponse] = ???

    override def getApplicationsByAgencies(): ServiceCall[AgenciesList, ApplicationByAgencyIdResponse] = ???

    override def portfoliosWithStatusByAgency(agencyId: String): ServiceCall[NotUsed, Seq[LeaseStatusRow]] = ???

    override def updateVATForAgency(agencyId: String): ServiceCall[VatUpdateFields, PortfolioList] = ???

    override def updateVatAllAgencies(): ServiceCall[VatUpdateFields, Seq[PortfolioList]] = ???

    override def getHistoricalCommandsByAgency(agencyId: String): ServiceCall[HistoricalEventRequest, Seq[LatestCommandsResponse]] = ???

    override def revertVATForAgency(agencyId: String): ServiceCall[NotUsed, Done] = ???
  }

  class WalletServiceStub(stub: ProducerStub[WalletEvent], blockchainStub: ProducerStub[BlockchainEvent]) extends WalletService {
    override def createReceipt(): ServiceCall[CreateReceiptRequest, Done] = ???

    override def getPartyBalances(party: String): ServiceCall[NotUsed, PartyBalancesResponse] = ???

    override def transfer(): ServiceCall[WalletTransferRequest, WalletTransactionResponse] = ???

    override def balancesStream(): ServiceCall[Source[StreamMessageInput, NotUsed], Source[StreamMessageOutput, NotUsed]] = ???

    override def retryPending(): ServiceCall[NotUsed, Done] = ???

    override def failTransaction(): ServiceCall[FailTransactionRequest, Done] = ???

    override def createWallet(): ServiceCall[CreateWalletRequest, WalletTransactionResponse] = ???

    override def createConsolidatedWallet(partyId: String): ServiceCall[NotUsed, Done] = ???

    override def reversePayments(): ServiceCall[Seq[UUID], Done] = ???

    override def demandPayments(): ServiceCall[Seq[UUID], Done] = ???

    override def payout(): ServiceCall[WalletPayoutRequest, WalletTransactionResponse] = ???

    override def payoutFromConsolidatedWallet(partyId: String): ServiceCall[WalletPayoutRequest, WalletTransactionResponse] = ???

    override def requestProofOfPayment(): ServiceCall[CreateProofOfPaymentRequest, ProofOfPaymentResponse] = ???

    override def getPaymentHistory(startDate: Option[String], endDate: Option[String]): ServiceCall[NotUsed, PaymentHistoryResponse] = ???

    override def lookupReference(reference: String): ServiceCall[NotUsed, ReferenceLookupResponse] = ???

    override def lookupIdentifier(identifier: String): ServiceCall[NotUsed, WalletTransactionResponse] = ???

    override def transferFunds(partyId: String): ServiceCall[CreateDepositTransferRequest, WalletTransactionResponse] = ???

    override def refundAccount(partyId: String): ServiceCall[CreateAccountRefundRequest, DepositTransferCreatedResponse] = ???

    override def refundDeposits(partyId: String): ServiceCall[ReleaseDeposit, Done] = ???

    override def irohaTransactions(walletRef: String, size: String, transaction: Option[String]): ServiceCall[NotUsed, IrohaResponses.TransactionResponse] = ???

    override def irohaPending(walletRef: String, size: String, transaction: Option[String]): ServiceCall[NotUsed, IrohaResponses.PendingResponse] = ???

    override def irohaBlock(walletRef: String, height: Int): ServiceCall[NotUsed, IrohaResponses.BlockResponse] = ???

    override def irohaRoles(walletRef: String): ServiceCall[NotUsed, IrohaResponses.RolesResponse] = ???

    override def irohaPermissions(walletRef: String, role: String): ServiceCall[NotUsed, IrohaResponses.PermissionsResponse] = ???

    override def walletEvents: Topic[WalletEvent] = stub.topic

    override def proofOfPaymentEvents: Topic[ProofOfPaymentEvent] = ???

    override def badReferenceEvents: Topic[BadReferenceEvent] = ???

    override def blockchainEvents: Topic[BlockchainEvent] = blockchainStub.topic

    override def statusEvents: Topic[StatusEvent] = ???

    override def getAllocatedBadReferences(): ServiceCall[NotUsed, Seq[BadReferenceTable]] = ???

    override def getUnallocatedBadReferences(): ServiceCall[NotUsed, Seq[BadReferenceTable]] = ???

    override def allocateBadReference(txId: String): ServiceCall[BadReferenceRequest, Done] = ???

    override def getProjections: ServiceCall[NotUsed, State] = ???

    override def startProjection(name: String): ServiceCall[NotUsed, Done] = ???

    override def stopProjection(name: String): ServiceCall[NotUsed, Done] = ???

    override def startProjectionWorker(name: String, worker: String): ServiceCall[NotUsed, Done] = ???

    override def stopProjectionWorker(name: String, worker: String): ServiceCall[NotUsed, Done] = ???

    override def bulkWalletTransfer(fromWalletRef: String): ServiceCall[Base64Request, Base64CsvResponse] = ???

    override def createWallets(): ServiceCall[Seq[CreateWalletRequest], Seq[WalletTransactionResponse]] = ???

    override def payoutFromTransactionalWallet(partyId: String): ServiceCall[WalletPayoutRequest, WalletTransactionResponse] = ???

    override def getAgencyPayments(startDate: Option[String], endDate: Option[String], agencyId: String): ServiceCall[NotUsed, Seq[PaymentByAgencyResponse]] = ???
  }
}

case class AgencyServiceStub(agencyEventStub: Any, agencyGroupEventStub: Any)(implicit val ec: ExecutionContext) extends AgencyService {

  override def createAgency: ServiceCall[Agency_v1, Agency] = ???

  override def updateAgency(id: util.UUID): ServiceCall[Agency_v1, Agency] = ???

  override def getAgency(id: util.UUID) = {
    ServiceCall { _ =>
      Future.successful(Agency(id,None, None, None, true))
    }
  }

  override def deactivateAgency(id: util.UUID): ServiceCall[DeactivationRequest, Done] = ???

  override def activateAgency(id: util.UUID): ServiceCall[ActivationRequest, Done] = ???

  override def revokeInvitation(partyId: String): ServiceCall[NotUsed, Invitation] = ???

  override def resendInvitation(partyId: String): ServiceCall[NotUsed, Invitation] = ???

  override def getInvitations(): ServiceCall[NotUsed, Seq[Invitation]] = ???

  override def createPartyInvitation(partyId: String): ServiceCall[InvitationOTPRequest, Invitation] = ???

  override def createInvitation(): ServiceCall[InvitationRequest, Invitation] = ???

  override def getInvitation(token: String): ServiceCall[NotUsed, InvitationReply] = ???

  override def getMembers(): ServiceCall[NotUsed, Seq[Member]] = ???

  override def removeMember(userID: util.UUID): ServiceCall[NotUsed, Seq[Member]] = ???

  override def getThemes(): ServiceCall[NotUsed, Seq[ThemeRgba]] = ???

  override def updateTheme(): ServiceCall[ThemeRef, Theme] = ???

  override def updateLogo(): ServiceCall[Logo, Logo] = ???

  override def agencyListing(): ServiceCall[NotUsed, Seq[AgencyListingResponse]] = ???

  override def getInvitableParties(): ServiceCall[NotUsed, Seq[InvitableParty]] = ???

  override def createSegment(): ServiceCall[CreateSegment, Seq[api.Segment]] = ???

  override def updateSegment(segmentId: String): ServiceCall[CreateSegment, Seq[api.Segment]] = ???

  override def getSegments(): ServiceCall[NotUsed, Seq[api.Segment]] = ???

  override def deleteSegment(id: String): ServiceCall[NotUsed, Seq[api.Segment]] = ???

  override def createAgencyGroup: ServiceCall[Company_v1, AgencyGroup] = ???

  override def updateAgencyGroup(id: String): ServiceCall[Company_v1, Done] = ???

  override def updateAgencyGroupRoyaltyPercentage(id: String): ServiceCall[Double, AgencyGroupResponse] = ???

  override def getAgencyGroup(id: String): ServiceCall[NotUsed, AgencyGroupResponse] = ???

  override def getAgencyGroupEntity(id: String): ServiceCall[NotUsed, AgencyGroupResponse] = ???

  override def addAgencyToGroup(id: String): ServiceCall[GroupMember, Done] = ???

  override def removeAgencyFromGroup(id: String): ServiceCall[GroupMember, Done] = ???

  override def removeAgencyFromGroupV2(groupId: String, agencyId: String): ServiceCall[NotUsed, Done] = ???

  override def markTestAgencies(): ServiceCall[TestAgenciesRequest, Done] = ???

  override def getAllIntegrations: ServiceCall[NotUsed, Map[String, Seq[IntegrationResponse]]] = ???

  override def getAgencyIntegrations: ServiceCall[NotUsed, Seq[IntegrationResponse]] = ???

  override def activateIntegration: ServiceCall[IntegrationRequest, Done] = ???

  override def refreshIntegration(id: String, agencyId: String): ServiceCall[IntegrationRequest, Seq[IntegrationResponse]] = ???

  override def refreshAllIntegrations(): ServiceCall[NotUsed, Done] = ???

  override def deactivateIntegration(integrationType: String): ServiceCall[NotUsed, Done] = ???

  override def deactivateIntegrationById(id: String): ServiceCall[NotUsed, Done] = ???

  override def updateIntegrationById(agencyId: String, id: String): ServiceCall[IntegrationRequest, Done] = ???

  override def createOAuthURLPropertyInspect(): ServiceCall[NotUsed, PropertyInspectOAuthResponse] = ???

  override def listAllIntegrationTypes(): ServiceCall[NotUsed, Seq[IntegrationTypeResponse]] = ???

  override def listPropertyInspectInspections() = ???

  override def listPropDataApplications() = ???

  override def searchPropDataApplications() = ???

  override def fetchPropDataApplication(id: String)= ???

  override def refreshPropDataAuthTokens(): ServiceCall[NotUsed, Done] = ???

  override def createAllDraftApplications(): ServiceCall[NotUsed, Done] = ???

  override def agencyEvents(): Topic[AgencyEvent] = ???

  override def agencyGroupEvents(): Topic[AgencyGroupEvent] = ???

  override def getProjections: ServiceCall[NotUsed, State] = ???

  override def startProjection(name: String): ServiceCall[NotUsed, Done] = ???

  override def stopProjection(name: String): ServiceCall[NotUsed, Done] = ???

  override def startProjectionWorker(name: String, worker: String): ServiceCall[NotUsed, Done] = ???

  override def stopProjectionWorker(name: String, worker: String): ServiceCall[NotUsed, Done] = ???
}
