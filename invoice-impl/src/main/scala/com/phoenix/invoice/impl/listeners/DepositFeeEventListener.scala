package com.phoenix.invoice.impl.listeners

import akka.Done
import akka.actor.ActorSystem
import akka.stream.Materializer
import akka.stream.scaladsl.Flow
import akka.util.Timeout
import com.lightbend.lagom.scaladsl.api.transport.NotFound
import com.lightbend.lagom.scaladsl.persistence.PersistentEntityRegistry
import com.phoenix.agency.api.AgencyService
import com.phoenix.authentication.jwt.{AgencyMembership, AuthenticationService, UserSession}
import com.phoenix.finance.Beneficiary
import com.phoenix.invoice.api.Accounting.{NoVatGroup, VatGroup15}
import com.phoenix.invoice.api.{Accounting, InvoiceBeneficiaryRule, InvoiceType}
import com.phoenix.invoice.impl.commands.CreateInvoice
import com.phoenix.invoice.impl.connectors.{DepositFeeConnector, DepositFeeTableRow}
import com.phoenix.invoice.impl.entities.InvoiceEntity
import com.phoenix.logging.EventLogging
import com.phoenix.party.api.AccountType
import com.phoenix.persistence.PersistentEntityRegistrySugar
import com.phoenix.portfolio.api._
import com.phoenix.wallet.api.BlockchainCommand.TransferAsset
import com.phoenix.wallet.api._
import com.typesafe.scalalogging.LazyLogging
import org.joda.time.DateTime

import java.util.UUID
import scala.concurrent.duration._
import scala.concurrent.{ExecutionContext, Future}
import scala.math.BigDecimal.RoundingMode

class DepositFeeEventListener(override val entityRegistry: PersistentEntityRegistry,
                              walletService: WalletService,
                              agencyService: AgencyService,
                              auth: AuthenticationService,
                              depositFeeConnector: DepositFeeConnector)
                             (implicit system: ActorSystem, mat: Materializer)
  extends PersistentEntityRegistrySugar
    with EventLogging
    with LazyLogging {
  import DepositFeeEventListener._

  implicit val ec: ExecutionContext = system.dispatcher
  implicit val timeout: Timeout = Timeout(60.seconds)
  private val vatPercentage = 0.15

  private def generateJwt(agencyId: UUID): String = {
    auth.generateToken(UserSession("", Some(AgencyMembership.ReadOnlyMember(agencyId.toString)), "", "", Some("invoice"), "", None, Some(true))).authToken
  }

  private def createInvoiceFromAssetCommands(addAsset: BlockchainCommand.AddAsset, transferAsset: TransferAsset,
                                             transactionHash: String, timestamp: DateTime): Future[Done] = {
    for {
      isUnprocessedDepositFee <- depositFeeConnector.findDepositFee(transactionHash).map(_.isEmpty)

      globalVat <- transferAsset.destAccount.agency match {
        case Some(agencyId) =>
          agencyService.getAgency(agencyId)
            .handleRequestHeader(auth.authenticatedWith(generateJwt(agencyId)))
            .invoke()
            .map(x => x.globalVatEnabled)
            .recover {
              case e:Exception => e.printStackTrace()
                throw NotFound("Agency not found from AgencyService")
            }
        case None =>
          logger.info("Agency is not defined for transfer asset and GlobalVat is set to true for deposit fee for SOURCE ACCOUNT: {}", transferAsset.destAccount)
          Future(true)
      }

      _ <- addAsset.cashDepositFee match {
        case Some(cashDepositFee) if isUnprocessedDepositFee =>
          val invoiceCommand = createInvoice(
            agencyId = transferAsset.destAccount.agency.get,
            partyId = transferAsset.destAccount.party.get,
            accountType = AccountType(transferAsset.destAccount.accountType.get),
            depositFeeInclVat = (cashDepositFee.amount * (1 + vatPercentage)).setScale(2, RoundingMode.HALF_EVEN),
            portfolioId = transferAsset.destAccount.lease,
            vatable = globalVat,
            depositFeeInfo = CreateInvoice.DepositFee(timestamp, addAsset.bankingUid, transactionHash, addAsset.receiptReference)
          )
          invoiceCommand.flatMap { id =>
            val row = DepositFeeTableRow(
              transactionHash = transactionHash,
              depositFee = (cashDepositFee.amount * (1 + vatPercentage)).setScale(2, RoundingMode.HALF_EVEN),
              partyId = transferAsset.destAccount.party.get,
              agencyId = transferAsset.destAccount.agency.get,
              invoiceId = id)
            depositFeeConnector.insertDepositFee(row)
          }
        case _ => Future(Done)
      }
    } yield Done
  }

  private def createInvoiceFromFeeUpdatedEvent(event: DepositFeeUpdated): Future[Done] = {
    for {
      isUnprocessedDepositFee <- depositFeeConnector.findDepositFee(event.transaction).map(_.isEmpty)

      _ <- if (isUnprocessedDepositFee) {
          val invoiceCommand = createInvoice(
            agencyId = event.partyAccount.agency.get,
            partyId = event.partyAccount.party.get,
            accountType = AccountType(event.partyAccount.accountType.get),
            depositFeeInclVat = (event.cashDepositFee * (1 + vatPercentage)).setScale(2, RoundingMode.HALF_EVEN),
            portfolioId = event.partyAccount.leaseId.map(_.uuid),
            vatable = true,
            depositFeeInfo = CreateInvoice.DepositFee(event.receiptTimestamp, event.identifier, event.transaction, Some(event.reference))
          )
          invoiceCommand.flatMap { id =>
            val row = DepositFeeTableRow(
              transactionHash = event.transaction,
              depositFee = (event.cashDepositFee * (1 + vatPercentage)).setScale(2, RoundingMode.HALF_EVEN),
              partyId = event.partyAccount.party.get,
              agencyId = event.partyAccount.agency.get,
              invoiceId = id)
            depositFeeConnector.insertDepositFee(row)
          }
      } else {
        Future(Done)
      }
    } yield Done
  }

  private def createInvoiceBeneficiaryRule(agencyId: UUID, amount: BigDecimal, vatable: Boolean = false): Seq[InvoiceBeneficiaryRule] = {
    Seq(InvoiceBeneficiaryRule(Beneficiary.PartyBeneficiary(partyId = agencyId,
      partyTag = "Agency",
      reference = "Cash Deposit Fee",
      amount = Some(amount),
      vat = if (vatable) Some(true) else Some(false),
      id = Some(UUID.randomUUID()))))
  }

  private def createInvoice(agencyId: UUID, partyId: UUID, accountType: AccountType, depositFeeInclVat: BigDecimal,
                            portfolioId: Option[UUID], vatable: Boolean,
                            depositFeeInfo: CreateInvoice.DepositFee): Future[UUID] = {
    val amount = Accounting.GrossIncome(depositFeeInclVat, if (vatable) VatGroup15() else NoVatGroup())
    val command = CreateInvoice(
      invoiceType = InvoiceType.CashDepositFee,
      customerId = partyId,
      customerTag = accountType,
      recurrence = Recurrence.OnceOff,
      incomeExpenses = createInvoiceBeneficiaryRule(agencyId, depositFeeInclVat, vatable),
      amount = Some(amount),
      portfolioId = portfolioId.map(com.phoenix.util.UUID.fromUUID),
      agencyId = agencyId,
      userId = partyId,
      invoiceTemplateId = None,
      dueDate = None,
      description = None,
      applicationId = None,
      depositFee = Some(depositFeeInfo)
    )
    val id = UUID.randomUUID()
    entityRegistry.refFor[InvoiceEntity](id.toString).ask(command).map(_ => id)
  }

  logger.info("Starting blockchainEvents listener")
  walletService.blockchainEvents
    .subscribe
    .withGroupId("walletBlockChainEvent0_1")
    .atLeastOnce(
      Flow[BlockchainEvent].mapAsync(1)(logEventAsync)
        .mapAsync(1) {
          case event @ BlockchainEvent(commands, creatorWalletString, transaction, timestamp) =>
            val addAssetResult = commands.collectFirst { case aa: BlockchainCommand.AddAsset => aa }
            val transferAssetResult = commands.collectFirst { case ta: BlockchainCommand.TransferAsset => ta }

            (addAssetResult, transferAssetResult) match {
              case (Some(addAsset), Some(transferAsset)) if transferAsset.destAccount.agency.isDefined
                && canProcessEvent(transferAsset.destAccount.agency.get, timestamp)
              => createInvoiceFromAssetCommands(addAsset, transferAsset, transaction, timestamp)
              case _ => Future(Done)
            }
          case _ => Future(Done)
        })

  logger.info("Starting walletEvents listener")
  walletService.walletEvents
    .subscribe
    .withGroupId("cashDepositFeeListener0_1")
    .atLeastOnce(
      Flow[WalletEvent].mapAsync(1)(logEventAsync)
        .mapAsync(1) {
          case event: DepositFeeUpdated if event.partyAccount.party.isDefined
            && event.partyAccount.agency.isDefined
            && canProcessEvent(event.partyAccount.agency.get, event.receiptTimestamp)
          => createInvoiceFromFeeUpdatedEvent(event)
          case _ => Future(Done)
        })
}

object DepositFeeEventListener {
  private val featureEnabledDate = DateTime.parse("2023-01-06").withTime(0, 0, 0, 0)
  private val enabledTestAgencies = Seq(
    "8ea2c882-bad8-4dce-b190-5ce0b0967c67", "8fd4726e-a1d8-4fb6-ad9e-25bd699d1b93", "c12bdf6a-95b8-486d-b598-03d0e04d8c97",
    "481e9492-20fb-43dd-a248-fbafdde139ef", "c8c21026-9c89-4e7b-9475-65a45f96a9ef", "56dfbd51-14b5-4c8b-84ca-88058fbbe662",
    "7a36a92c-0254-494b-baa5-d93418672a1c").map(UUID.fromString)

  def isFeatureEnabled(agencyId: UUID, timestamp: DateTime): Boolean = {
    timestamp.isAfter(featureEnabledDate) || enabledTestAgencies.contains(agencyId)
  }
  def eventNotExpired(time: DateTime): Boolean = {
    val timeCheck = DateTime.now().minusMonths(1).plusDays(1)
    timeCheck.isBefore(time)
  }

  def canProcessEvent(agencyId: UUID, timestamp: DateTime): Boolean = {
    isFeatureEnabled(agencyId, timestamp) && eventNotExpired(timestamp)
  }
}
