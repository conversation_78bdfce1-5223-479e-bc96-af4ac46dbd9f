package com.phoenix.invoice.impl

import akka.actor.ActorSystem
import akka.actor.typed.scaladsl.adapter._
import akka.actor.typed.{ActorRef, ActorSystem => TypedActorSystem}
import akka.cluster.typed.ClusterSingleton
import akka.serialization.SerializationExtension
import akka.stream.Materializer
import com.lightbend.lagom.internal.client.CircuitBreakerMetricsProviderImpl
import com.lightbend.lagom.scaladsl.akka.discovery.AkkaDiscoveryComponents
import com.lightbend.lagom.scaladsl.broker.kafka.LagomKafkaComponents
import com.lightbend.lagom.scaladsl.devmode.LagomDevModeComponents
import com.lightbend.lagom.scaladsl.persistence.cassandra.CassandraPersistenceComponents
import com.lightbend.lagom.scaladsl.pubsub.{PubSubComponents, PubSubRegistry}
import com.lightbend.lagom.scaladsl.server._
import com.phoenix.agency.api.AgencyService
import com.phoenix.authentication.jwt.AuthenticationComponents
import com.phoenix.exception.CustomExceptionSerializer
import com.phoenix.invoice.api.InvoiceService
import com.phoenix.invoice.impl.actors.{MonthlyInvoiceScheduler, SimpleBatchInvoiceScheduler}
import com.phoenix.invoice.impl.connectors._
import com.phoenix.invoice.impl.entities.{BatchEntity, InvoiceEntity}
import com.phoenix.invoice.impl.listeners._
import com.phoenix.invoice.impl.readside.{BatchInvoiceReadSideProcessor, InvoiceEventReadSideProcessor}
import com.phoenix.party.api.PartyService
import com.phoenix.portfolio.api.PortfolioService
import com.phoenix.projections.lib.ProjectionRegistryProxy
import com.phoenix.wallet.api.WalletService
import com.softwaremill.macwire._
import play.api.Environment
import play.api.libs.ws.ahc.AhcWSComponents

import scala.concurrent.ExecutionContext

// Component trait defining shared dependencies
trait InvoiceAppComponents extends LagomServerComponents
  with AuthenticationComponents
  with CassandraPersistenceComponents {

  implicit def executionContext: ExecutionContext
  def environment: Environment
  val pubSubRegistry: PubSubRegistry
  implicit def actorSys: ActorSystem
  implicit val typedSystem: TypedActorSystem[Nothing] = actorSystem.toTyped
  implicit val materializer: Materializer
  implicit val monthlyInvoiceSchedulerProxy: ActorRef[MonthlyInvoiceScheduler.Command]

  // Dependencies wired with Macwire
  lazy val invoiceRepository = wire[InvoiceRepository]
  lazy val propertyConnector = wire[PropertyConnector]
  lazy val partyRepository = wire[PartyRepository]
  lazy val portfolioConnector = wire[PortfolioConnector]
  lazy val depositFeeConnector = wire[DepositFeeConnector]
  lazy val invoiceSegmentFilterImpl = wire[InvoiceSegmentFilterImpl]
  lazy val jsonSerializerRegistry = InvoiceSerializerRegistry
  lazy val phoenixProjectionRegistry = wire[ProjectionRegistryProxy]
  lazy val serialization = SerializationExtension(actorSystem)

  // Register persistent entities and read-side processors
  persistentEntityRegistry.register(wire[InvoiceEntity])
  persistentEntityRegistry.register(wire[BatchEntity])
  readSide.register(wire[InvoiceEventReadSideProcessor])
  readSide.register(wire[BatchInvoiceReadSideProcessor])
}

// Abstract application class with core functionality
abstract class InvoiceApplication(context: LagomApplicationContext) extends LagomApplication(context)
  with InvoiceAppComponents
  with AhcWSComponents
  with PubSubComponents {

  override lazy val defaultExceptionSerializer = new CustomExceptionSerializer(environment,
    title = "invoice",
    version = getClass.getPackage.getImplementationVersion)
  implicit def actorSys: ActorSystem = actorSystem
  lazy val singletonManager = ClusterSingleton(typedSystem)

  // External service implementations
  lazy val portfolioService: PortfolioService = serviceClient.implement[PortfolioService]
  lazy val partyService: PartyService = serviceClient.implement[PartyService]
  lazy val walletService: WalletService = serviceClient.implement[WalletService]
  lazy val agencyService: AgencyService = serviceClient.implement[AgencyService]

  // Scheduler actors
  lazy val monthlyInvoiceScheduler = wire[MonthlyInvoiceScheduler]
  lazy val simpleBatchInvoiceScheduler = wire[SimpleBatchInvoiceScheduler]
  implicit lazy val monthlyInvoiceSchedulerProxy: ActorRef[MonthlyInvoiceScheduler.Command] = {
    monthlyInvoiceScheduler.initWithTimers
  }

  // Lagom server definition
  override lazy val lagomServer = serverFor[InvoiceService](wire[InvoiceServiceImpl])

  // Subscription method for event listeners
  def subscribeSome(listeners: String*)(implicit executionContext: ExecutionContext): Unit = {
    val all = listeners.contains("*")
    if (all || listeners.contains("portfolio")) wire[PortfolioEventListener]
    if (all || listeners.contains("party")) wire[PartyEventListener]
    if (all || listeners.contains("property")) wire[PropertyEventListener]
    if (all || listeners.contains("applicationFee")) wire[ApplicationFeeEventListener]
    if (all || listeners.contains("monthlyInvoiceScheduler")) monthlyInvoiceSchedulerProxy
    if (all || listeners.contains("simpleBatchInvoiceScheduler")) simpleBatchInvoiceScheduler.initWithTimers
    if (all || listeners.contains("depositFee")) wire[DepositFeeEventListener]
  }

  def subscribeAll()(implicit executionContext: ExecutionContext): Unit = subscribeSome("*")
}

// Application loader for different environments
class InvoiceLoader extends LagomApplicationLoader {
  override def loadDevMode(context: LagomApplicationContext): LagomApplication =
    new InvoiceApplication(context) with LagomDevModeComponents with LagomKafkaComponents {
      subscribeAll()
    }

  override def load(context: LagomApplicationContext): LagomApplication =
    new InvoiceApplication(context) with AkkaDiscoveryComponents with LagomKafkaComponents {
      override lazy val circuitBreakerMetricsProvider = new CircuitBreakerMetricsProviderImpl(actorSystem)
      subscribeAll()
    }

  override def describeService = Some(readDescriptor[InvoiceService])
}
